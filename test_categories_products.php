<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// إنشاء تطبيق Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// محاكاة request
$request = Request::create('/admin/category/add', 'GET');
$response = $kernel->handle($request);

echo "Testing Categories and Products Display\n";
echo "=====================================\n\n";

// تعيين الـ module
session()->put('current_module', 2);
\Illuminate\Support\Facades\Config::set('module.current_module_id', 2);

echo "Current Module ID: " . config('module.current_module_id') . "\n";

// اختبار الأقسام
echo "\n1. Testing Categories:\n";
echo "---------------------\n";

try {
    $categoryRepo = app(\App\Contracts\Repositories\CategoryRepositoryInterface::class);
    $categories = $categoryRepo->getListWhere(filters: ['position' => 0]);
    
    echo "✓ Categories loaded successfully\n";
    echo "✓ Total categories: " . $categories->count() . "\n";
    
    foreach($categories->take(3) as $category) {
        echo "  - " . $category->name . " (Module: " . $category->module_id . ")\n";
    }
    
} catch (\Exception $e) {
    echo "✗ Error loading categories: " . $e->getMessage() . "\n";
}

// اختبار المنتجات
echo "\n2. Testing Products:\n";
echo "-------------------\n";

try {
    $items = \App\Models\Item::where('module_id', 2)->take(5)->get();
    
    echo "✓ Products loaded successfully\n";
    echo "✓ Total products for module 2: " . \App\Models\Item::where('module_id', 2)->count() . "\n";
    
    foreach($items as $item) {
        echo "  - " . $item->name . " (Module: " . $item->module_id . ")\n";
    }
    
} catch (\Exception $e) {
    echo "✗ Error loading products: " . $e->getMessage() . "\n";
}

// اختبار الـ CategoryController
echo "\n3. Testing CategoryController:\n";
echo "-----------------------------\n";

try {
    $controller = app(\App\Http\Controllers\Admin\Item\CategoryController::class);
    $request = new \Illuminate\Http\Request();
    $request->merge(['position' => 0]);
    
    $view = $controller->index($request);
    $data = $view->getData();
    
    echo "✓ CategoryController working\n";
    echo "✓ View: " . $view->getName() . "\n";
    echo "✓ Categories in view: " . $data['categories']->count() . "\n";
    
} catch (\Exception $e) {
    echo "✗ Error in CategoryController: " . $e->getMessage() . "\n";
}

echo "\n4. Testing Module Configuration:\n";
echo "-------------------------------\n";

$modules = \App\Models\Module::where('status', 1)->get();
echo "✓ Active modules: " . $modules->count() . "\n";

foreach($modules as $module) {
    echo "  - " . $module->module_name . " (ID: " . $module->id . ", Type: " . $module->module_type . ")\n";
}

echo "\n✓ All tests completed!\n";
echo "The categories and products should now display correctly in the admin panel.\n";

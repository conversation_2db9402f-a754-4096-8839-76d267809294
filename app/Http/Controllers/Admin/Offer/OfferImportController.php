<?php

namespace App\Http\Controllers\Admin\Offer;

use App\Models\Item;
use App\Models\FlashSale;
use App\Models\FlashSaleItem;
use App\Models\Translation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class OfferImportController extends Controller
{
    /**
     * عرض صفحة استيراد العروض
     */
    public function index()
    {
        return view('admin-views.offer.import-from-api');
    }

    /**
     * بدء عملية استيراد العروض من API
     */
    public function startImport(): JsonResponse
    {
        try {
            // جلب البيانات من الـ API
            $response = Http::timeout(60)->get('https://albayan.live/ApiWithAmeen/public/api/HemamSpecialOfferData');

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال بـ API: ' . $response->status()
                ]);
            }

            $apiOffers = $response->json();

            if (empty($apiOffers)) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد عروض في API'
                ]);
            }

            $results = [
                'imported' => 0,
                'updated' => 0,
                'skipped' => 0,
                'errors' => 0
            ];

            foreach ($apiOffers as $apiOffer) {
                try {
                    $result = $this->processOffer($apiOffer);
                    $results[$result]++;
                } catch (\Exception $e) {
                    $results['errors']++;
                    Log::error('Error processing offer: ' . $e->getMessage(), ['offer' => $apiOffer]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'تم الاستيراد بنجاح!',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Error in offer import: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء الاستيراد: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معالجة عرض واحد
     */
    private function processOffer(array $apiOffer): string
    {
        $offerGuid = $apiOffer['Guid'] ?? '';
        $offerName = trim($apiOffer['Name'] ?? '');

        if (empty($offerGuid) || empty($offerName)) {
            return 'skipped';
        }

        // التحقق من وجود العرض (نبحث في title لأن Flash Sale لا يحتوي على code)
        $existingFlashSale = FlashSale::where('title', 'LIKE', '%' . $offerName . '%')->first();

        if ($existingFlashSale) {
            // تحديث العرض الموجود
            $this->updateExistingOffer($existingFlashSale, $apiOffer);
            return 'updated';
        } else {
            // إنشاء عرض جديد
            $this->createNewOffer($apiOffer);
            return 'imported';
        }
    }

    /**
     * إنشاء عرض جديد
     */
    private function createNewOffer(array $apiOffer): void
    {
        $offerName = trim($apiOffer['Name'] ?? '');
        $offerGuid = $apiOffer['Guid'] ?? '';
        $discount = floatval($apiOffer['Discount'] ?? 0) * 100; // تحويل إلى نسبة مئوية
        $startDate = $this->parseDate($apiOffer['StartDate'] ?? '');
        $endDate = $this->parseDate($apiOffer['EndDate'] ?? '');
        $isActive = $apiOffer['Active'] == '1';

        // إنشاء Flash Sale
        $flashSale = new FlashSale();
        $flashSale->title = $offerName;
        $flashSale->start_date = $startDate;
        $flashSale->end_date = $endDate;
        $flashSale->module_id = Config::get('module.current_module_id', 1);
        $flashSale->is_publish = $isActive ? 1 : 0;
        $flashSale->admin_discount_percentage = $discount; // نسبة خصم الإدارة
        $flashSale->vendor_discount_percentage = 100 - $discount; // الباقي للمتجر
        $flashSale->save();

        // إضافة الترجمات
        $this->addOfferTranslations($flashSale, $offerName);

        // معالجة المنتجات المرتبطة بالعرض
        if (!empty($apiOffer['Mat']) && is_array($apiOffer['Mat'])) {
            $this->processOfferProducts($flashSale, $apiOffer['Mat']);
        }

        Log::info("Created flash sale: {$offerName} with GUID {$offerGuid}");
    }

    /**
     * تحديث عرض موجود
     */
    private function updateExistingOffer(FlashSale $flashSale, array $apiOffer): void
    {
        $offerName = trim($apiOffer['Name'] ?? '');
        $discount = floatval($apiOffer['Discount'] ?? 0) * 100; // تحويل إلى نسبة مئوية
        $startDate = $this->parseDate($apiOffer['StartDate'] ?? '');
        $endDate = $this->parseDate($apiOffer['EndDate'] ?? '');
        $isActive = $apiOffer['Active'] == '1';

        // تحديث البيانات
        $flashSale->title = $offerName;
        $flashSale->start_date = $startDate;
        $flashSale->end_date = $endDate;
        $flashSale->admin_discount_percentage = $discount;
        $flashSale->vendor_discount_percentage = 100 - $discount;
        $flashSale->is_publish = $isActive ? 1 : 0;
        $flashSale->save();

        // تحديث الترجمات
        $this->updateOfferTranslations($flashSale, $offerName);

        Log::info("Updated flash sale: {$offerName} with ID {$flashSale->id}");
    }

    /**
     * إضافة ترجمات للعرض
     */
    private function addOfferTranslations(FlashSale $flashSale, string $arabicName): void
    {
        $translations = [
            ['locale' => 'ar', 'key' => 'title', 'value' => $arabicName],
            ['locale' => 'en', 'key' => 'title', 'value' => $arabicName], // يمكن إضافة ترجمة إنجليزية
        ];

        foreach ($translations as $translation) {
            Translation::updateOrCreate([
                'translationable_type' => FlashSale::class,
                'translationable_id' => $flashSale->id,
                'locale' => $translation['locale'],
                'key' => $translation['key'],
            ], [
                'value' => $translation['value']
            ]);
        }
    }

    /**
     * تحديث ترجمات العرض
     */
    private function updateOfferTranslations(FlashSale $flashSale, string $arabicName): void
    {
        Translation::where('translationable_type', FlashSale::class)
            ->where('translationable_id', $flashSale->id)
            ->where('key', 'title')
            ->update(['value' => $arabicName]);
    }

    /**
     * معالجة المنتجات المرتبطة بالعرض
     */
    private function processOfferProducts(FlashSale $flashSale, array $products): void
    {
        foreach ($products as $product) {
            $productGuid = $product['GUID'] ?? '';
            $productQty = floatval($product['Qty'] ?? 1);
            $productPrice = floatval($product['EndUser'] ?? 0);

            if (empty($productGuid)) {
                continue;
            }

            // البحث عن المنتج في قاعدة البيانات
            $item = Item::where('guid', $productGuid)->first();

            if (!$item) {
                Log::warning("Product with GUID {$productGuid} not found for flash sale {$flashSale->title}");
                continue;
            }

            // التحقق من وجود المنتج في Flash Sale مسبق<|im_start|>
            $existingFlashSaleItem = FlashSaleItem::where('flash_sale_id', $flashSale->id)
                ->where('item_id', $item->id)
                ->first();

            if (!$existingFlashSaleItem) {
                // إنشاء Flash Sale Item جديد
                $flashSaleItem = new FlashSaleItem();
                $flashSaleItem->flash_sale_id = $flashSale->id;
                $flashSaleItem->item_id = $item->id;
                $flashSaleItem->stock = $productQty;
                $flashSaleItem->available_stock = $productQty;
                $flashSaleItem->discount_type = 'percent';
                $flashSaleItem->discount = $flashSale->admin_discount_percentage;

                // حساب مبلغ الخصم
                $discountAmount = ($item->price / 100) * $flashSale->admin_discount_percentage;
                $flashSaleItem->discount_amount = $discountAmount;
                $flashSaleItem->price = $item->price - $discountAmount;

                $flashSaleItem->save();

                Log::info("Added product {$item->name} to flash sale {$flashSale->title}");
            }
        }
    }

    /**
     * تحويل التاريخ من صيغة API إلى صيغة Laravel
     */
    private function parseDate(string $dateString): ?string
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            // تحويل التاريخ من صيغة "2025-07-07 00:00:00.000" إلى "Y-m-d"
            $date = Carbon::createFromFormat('Y-m-d H:i:s.v', $dateString);
            return $date->format('Y-m-d');
        } catch (\Exception $e) {
            try {
                // محاولة أخرى بصيغة مختلفة
                $date = Carbon::createFromFormat('Y-m-d H:i:s', $dateString);
                return $date->format('Y-m-d');
            } catch (\Exception $e) {
                Log::warning("Could not parse date: {$dateString}");
                return null;
            }
        }
    }
}

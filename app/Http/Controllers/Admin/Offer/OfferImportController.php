<?php

namespace App\Http\Controllers\Admin\Offer;

use App\Models\Item;
use App\Models\Coupon;
use App\Models\Campaign;
use App\Models\Translation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class OfferImportController extends Controller
{
    /**
     * عرض صفحة استيراد العروض
     */
    public function index()
    {
        return view('admin-views.offer.import-from-api');
    }

    /**
     * بدء عملية استيراد العروض من API
     */
    public function startImport(): JsonResponse
    {
        try {
            // جلب البيانات من الـ API
            $response = Http::timeout(60)->get('https://albayan.live/ApiWithAmeen/public/api/HemamSpecialOfferData');

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال بـ API: ' . $response->status()
                ]);
            }

            $apiOffers = $response->json();

            if (empty($apiOffers)) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد عروض في API'
                ]);
            }

            $results = [
                'imported' => 0,
                'updated' => 0,
                'skipped' => 0,
                'errors' => 0
            ];

            foreach ($apiOffers as $apiOffer) {
                try {
                    $result = $this->processOffer($apiOffer);
                    $results[$result]++;
                } catch (\Exception $e) {
                    $results['errors']++;
                    Log::error('Error processing offer: ' . $e->getMessage(), ['offer' => $apiOffer]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'تم الاستيراد بنجاح!',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Error in offer import: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء الاستيراد: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معالجة عرض واحد
     */
    private function processOffer(array $apiOffer): string
    {
        $offerGuid = $apiOffer['Guid'] ?? '';
        $offerName = trim($apiOffer['Name'] ?? '');

        if (empty($offerGuid) || empty($offerName)) {
            return 'skipped';
        }

        // التحقق من وجود العرض
        $existingCoupon = Coupon::where('code', $offerGuid)->first();

        if ($existingCoupon) {
            // تحديث العرض الموجود
            $this->updateExistingOffer($existingCoupon, $apiOffer);
            return 'updated';
        } else {
            // إنشاء عرض جديد
            $this->createNewOffer($apiOffer);
            return 'imported';
        }
    }

    /**
     * إنشاء عرض جديد
     */
    private function createNewOffer(array $apiOffer): void
    {
        $offerName = trim($apiOffer['Name'] ?? '');
        $offerGuid = $apiOffer['Guid'] ?? '';
        $discount = floatval($apiOffer['Discount'] ?? 0);
        $discountType = $apiOffer['DiscountType'] == '1' ? 'percent' : 'amount';
        $startDate = $this->parseDate($apiOffer['StartDate'] ?? '');
        $endDate = $this->parseDate($apiOffer['EndDate'] ?? '');
        $isActive = $apiOffer['Active'] == '1';

        // إنشاء الكوبون
        $coupon = new Coupon();
        $coupon->title = $offerName;
        $coupon->code = $offerGuid;
        $coupon->start_date = $startDate;
        $coupon->expire_date = $endDate;
        $coupon->min_purchase = 0; // يمكن تعديله حسب الحاجة
        $coupon->max_discount = 0; // بدون حد أقصى
        $coupon->discount = $discount;
        $coupon->discount_type = $discountType;
        $coupon->coupon_type = 'default';
        $coupon->limit = null; // بدون حد للاستخدام
        $coupon->status = $isActive ? 1 : 0;
        $coupon->module_id = Config::get('module.current_module_id', 1);
        $coupon->created_by = 'admin';
        $coupon->store_id = 1; // يمكن تعديله حسب الحاجة
        $coupon->save();

        // إضافة الترجمات
        $this->addOfferTranslations($coupon, $offerName);

        // معالجة المنتجات المرتبطة بالعرض
        if (!empty($apiOffer['Mat']) && is_array($apiOffer['Mat'])) {
            $this->processOfferProducts($coupon, $apiOffer['Mat']);
        }

        Log::info("Created offer: {$offerName} with code {$offerGuid}");
    }

    /**
     * تحديث عرض موجود
     */
    private function updateExistingOffer(Coupon $coupon, array $apiOffer): void
    {
        $offerName = trim($apiOffer['Name'] ?? '');
        $discount = floatval($apiOffer['Discount'] ?? 0);
        $discountType = $apiOffer['DiscountType'] == '1' ? 'percent' : 'amount';
        $startDate = $this->parseDate($apiOffer['StartDate'] ?? '');
        $endDate = $this->parseDate($apiOffer['EndDate'] ?? '');
        $isActive = $apiOffer['Active'] == '1';

        // تحديث البيانات
        $coupon->title = $offerName;
        $coupon->start_date = $startDate;
        $coupon->expire_date = $endDate;
        $coupon->discount = $discount;
        $coupon->discount_type = $discountType;
        $coupon->status = $isActive ? 1 : 0;
        $coupon->save();

        // تحديث الترجمات
        $this->updateOfferTranslations($coupon, $offerName);

        Log::info("Updated offer: {$offerName} with code {$coupon->code}");
    }

    /**
     * إضافة ترجمات للعرض
     */
    private function addOfferTranslations(Coupon $coupon, string $arabicName): void
    {
        $translations = [
            ['locale' => 'ar', 'key' => 'title', 'value' => $arabicName],
            ['locale' => 'en', 'key' => 'title', 'value' => $arabicName], // يمكن إضافة ترجمة إنجليزية
        ];

        foreach ($translations as $translation) {
            Translation::updateOrCreate([
                'translationable_type' => Coupon::class,
                'translationable_id' => $coupon->id,
                'locale' => $translation['locale'],
                'key' => $translation['key'],
            ], [
                'value' => $translation['value']
            ]);
        }
    }

    /**
     * تحديث ترجمات العرض
     */
    private function updateOfferTranslations(Coupon $coupon, string $arabicName): void
    {
        Translation::where('translationable_type', Coupon::class)
            ->where('translationable_id', $coupon->id)
            ->where('key', 'title')
            ->update(['value' => $arabicName]);
    }

    /**
     * معالجة المنتجات المرتبطة بالعرض
     */
    private function processOfferProducts(Coupon $coupon, array $products): void
    {
        $productGuids = [];
        
        foreach ($products as $product) {
            $productGuid = $product['GUID'] ?? '';
            if (!empty($productGuid)) {
                $productGuids[] = $productGuid;
            }
        }

        if (!empty($productGuids)) {
            // يمكن إضافة منطق ربط المنتجات بالعرض هنا
            // مثلاً حفظ معرفات المنتجات في حقل data
            $coupon->data = json_encode(['product_guids' => $productGuids]);
            $coupon->save();
            
            Log::info("Linked " . count($productGuids) . " products to offer: {$coupon->title}");
        }
    }

    /**
     * تحويل التاريخ من صيغة API إلى صيغة Laravel
     */
    private function parseDate(string $dateString): ?string
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            // تحويل التاريخ من صيغة "2025-07-07 00:00:00.000" إلى "Y-m-d"
            $date = Carbon::createFromFormat('Y-m-d H:i:s.v', $dateString);
            return $date->format('Y-m-d');
        } catch (\Exception $e) {
            try {
                // محاولة أخرى بصيغة مختلفة
                $date = Carbon::createFromFormat('Y-m-d H:i:s', $dateString);
                return $date->format('Y-m-d');
            } catch (\Exception $e) {
                Log::warning("Could not parse date: {$dateString}");
                return null;
            }
        }
    }
}

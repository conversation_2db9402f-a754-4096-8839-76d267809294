<?php

namespace App\Http\Controllers\Admin\Item;

use App\Contracts\Repositories\UnitRepositoryInterface;
use App\Contracts\Repositories\TranslationRepositoryInterface;
use App\Enums\ExportFileNames\Admin\Unit;
use App\Enums\ViewPaths\Admin\Unit as UnitViewPath;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Admin\UnitAddRequest;
use App\Http\Requests\Admin\UnitUpdateRequest;
use App\Services\UnitService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use OpenSpout\Common\Exception\InvalidArgumentException;
use OpenSpout\Common\Exception\IOException;
use OpenSpout\Common\Exception\UnsupportedTypeException;
use OpenSpout\Writer\Exception\WriterNotOpenedException;
use Rap2hpoutre\FastExcel\FastExcel;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class UnitController extends BaseController
{
    public function __construct(
        protected UnitRepositoryInterface $unitRepo,
        protected UnitService $unitService,
        protected TranslationRepositoryInterface $translationRepo
    )
    {
    }

    public function index(?Request $request): View|Collection|LengthAwarePaginator|null
    {
        return $this->getListView();
    }

    private function getListView(): View
    {

        $units = $this->unitRepo->getListWhere(
            searchValue: request()?->search,
            dataLimit: config('default_pagination')
        );
        $language = getWebConfig('language');
        $defaultLang = str_replace('_', '-', app()->getLocale());
        return view(UnitViewPath::INDEX[VIEW], compact('units','language','defaultLang'));
    }

    public function add(UnitAddRequest $request): RedirectResponse
    {
        $unit = $this->unitRepo->add(data: $this->unitService->getAddData(request: $request));
        $this->translationRepo->addByModel(request: $request, model: $unit, modelPath: 'App\Models\Unit', attribute: 'unit');
        Toastr::success(translate('messages.unit_added_successfully'));
        return back();
    }

    public function getUpdateView(string|int $id): View
    {
        $unit = $this->unitRepo->getFirstWithoutGlobalScopeWhere(params: ['id' => $id]);
        $language = getWebConfig('language');
        $defaultLang = str_replace('_', '-', app()->getLocale());
        return view(UnitViewPath::UPDATE[VIEW], compact('unit','language','defaultLang'));
    }

    public function update(UnitUpdateRequest $request, $id): RedirectResponse
    {
        $unit = $this->unitRepo->update(id: $id ,data: $this->unitService->getAddData(request: $request));
        $this->translationRepo->updateByModel(request: $request, model: $unit, modelPath: 'App\Models\Unit', attribute: 'unit');
        Toastr::success(translate('messages.unit_updated_successfully'));
        return back();
    }

    public function delete(Request $request): RedirectResponse
    {
        $this->unitRepo->delete(id: $request['id']);
        Toastr::success(translate('messages.unit_deleted_successfully'));
        return back();
    }

    /**
     * @throws WriterNotOpenedException
     * @throws IOException
     * @throws UnsupportedTypeException
     * @throws InvalidArgumentException
     */
    public function exportList(string $type): StreamedResponse|string
    {
        $collection = $this->unitRepo->getList();

        if($type == 'excel'){
            return (new FastExcel($this->unitService->processExportData(collection: $collection)))->download(Unit::EXPORT_XLSX);
        }else{
            return (new FastExcel($this->unitService->processExportData(collection: $collection)))->download(Unit::EXPORT_CSV);
        }
    }

    public function search(Request $request): JsonResponse
    {
        $units = $this->unitRepo->getListWhere(
            searchValue: $request['search'],
            dataLimit: 50
        );

        return response()->json([
            'view'=>view(UnitViewPath::SEARCH[VIEW],compact('units'))->render()
        ]);
    }

    public function importFromApi(): RedirectResponse
    {
        try {
            // استدعاء API لجلب الوحدات
            $response = Http::timeout(30)->get('https://albayan.live/ApiWithAmeen/public/api/HemamUnityData');

            if (!$response->successful()) {
                Toastr::error('فشل في جلب البيانات من الـ API');
                return back();
            }

            $apiUnits = $response->json();

            if (empty($apiUnits)) {
                Toastr::warning('لا توجد بيانات في الـ API');
                return back();
            }

            $importedCount = 0;
            $updatedCount = 0;
            $skippedCount = 0;

            DB::beginTransaction();

            foreach ($apiUnits as $apiUnit) {
                $unitName = trim($apiUnit['Unit'] ?? '');

                if (empty($unitName)) {
                    ++$skippedCount;
                    continue;
                }

                // التحقق من وجود الوحدة
                $existingUnit = $this->unitRepo->getFirstWhere(['unit' => $unitName]);

                if ($existingUnit) {
                    // تحديث الترجمات للوحدة الموجودة
                    $this->updateUnitTranslationsFromApi($existingUnit, $unitName);
                    ++$updatedCount;
                    continue;
                }

                // إنشاء وحدة جديدة
                $unitData = [
                    'unit' => $unitName,
                ];

                $unit = $this->unitRepo->add($unitData);

                // إضافة الترجمات
                $this->addUnitTranslationsFromApi($unit, $unitName);

                ++$importedCount;
            }

            DB::commit();

            // إنشاء رسالة مفصلة
            $message = "تم استيراد {$importedCount} وحدة جديدة، وتحديث {$updatedCount} وحدة موجودة";
            if ($skippedCount > 0) {
                $message .= "، وتخطي {$skippedCount} وحدة فارغة";
            }

            Toastr::success($message);

        } catch (\Exception $e) {
            DB::rollBack();
            Toastr::error('حدث خطأ أثناء استيراد الوحدات: ' . $e->getMessage());
        }

        return back();
    }

    private function addUnitTranslationsFromApi($unit, string $unitName): void
    {
        // إضافة الترجمة العربية
        \App\Models\Translation::updateOrCreate([
            'translationable_type' => 'App\Models\Unit',
            'translationable_id' => $unit->id,
            'locale' => 'ar',
            'key' => 'unit',
        ], [
            'value' => $unitName,
        ]);

        // إضافة الترجمة الإنجليزية (نفس النص مؤقتاً)
        \App\Models\Translation::updateOrCreate([
            'translationable_type' => 'App\Models\Unit',
            'translationable_id' => $unit->id,
            'locale' => 'en',
            'key' => 'unit',
        ], [
            'value' => $unitName,
        ]);
    }

    private function updateUnitTranslationsFromApi($unit, string $unitName): void
    {
        // تحديث الاسم الأساسي إذا كان مختلفاً
        if ($unit->unit !== $unitName) {
            $this->unitRepo->update($unit->id, ['unit' => $unitName]);
        }

        // تحديث الترجمة العربية
        \App\Models\Translation::updateOrCreate([
            'translationable_type' => 'App\Models\Unit',
            'translationable_id' => $unit->id,
            'locale' => 'ar',
            'key' => 'unit',
        ], [
            'value' => $unitName,
        ]);

        // تحديث الترجمة الإنجليزية (نفس النص مؤقتاً)
        \App\Models\Translation::updateOrCreate([
            'translationable_type' => 'App\Models\Unit',
            'translationable_id' => $unit->id,
            'locale' => 'en',
            'key' => 'unit',
        ], [
            'value' => $unitName,
        ]);
    }
}

<?php

namespace App\Http\Controllers\Admin\Item;

use App\Http\Controllers\BaseController;
use App\Contracts\Repositories\CategoryRepositoryInterface;
use App\Contracts\Repositories\UnitRepositoryInterface;
use App\Contracts\Repositories\TranslationRepositoryInterface;
use App\Models\Item;
use App\Models\AddOn;
use App\Models\Category;
use App\Models\Unit;
use App\Models\Translation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Brian2694\Toastr\Facades\Toastr;

class ItemImportController extends BaseController
{
    public function __construct(
        protected CategoryRepositoryInterface $categoryRepo,
        protected UnitRepositoryInterface $unitRepo,
        protected TranslationRepositoryInterface $translationRepo
    ) {
    }

    /**
     * عرض صفحة استيراد المنتجات
     */
    public function index(): View
    {
        $moduleId = config('module.current_module_id');

        // إحصائيات أساسية
        $stats = [
            'total_items' => Item::where('module_id', $moduleId)->count(),
            'total_categories' => Category::where('module_id', $moduleId)->count(),
            'total_units' => Unit::count(),
            'total_addons' => AddOn::count(),
        ];

        return view('admin-views.product.import-from-api', compact('stats'));
    }

    /**
     * جلب إحصائيات من الـ API
     */
    public function getApiStats(): JsonResponse
    {
        try {
            $response = Http::timeout(30)->get('https://albayan.live/ApiWithAmeen/public/api/HemammaterialsData');

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في الاتصال بالـ API'
                ]);
            }

            $apiItems = $response->json();

            if (empty($apiItems)) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد بيانات في الـ API'
                ]);
            }

            // تحليل البيانات
            $stats = [
                'total_api_items' => count($apiItems),
                'items_with_addons' => 0,
                'total_addons' => 0,
                'categories_needed' => [],
                'units_needed' => [],
            ];

            foreach ($apiItems as $item) {
                // عد العناصر التي لها إضافات
                if (!empty($item['Rest']) && is_array($item['Rest'])) {
                    $stats['items_with_addons']++;
                    $stats['total_addons'] += count($item['Rest']);
                }

                // جمع الأقسام المطلوبة
                if (!empty($item['GroupGUID'])) {
                    $stats['categories_needed'][] = $item['GroupGUID'];
                }

                // جمع الوحدات المطلوبة
                if (!empty(trim($item['Unity'] ?? ''))) {
                    $stats['units_needed'][] = trim($item['Unity']);
                }
            }

            $stats['categories_needed'] = array_unique($stats['categories_needed']);
            $stats['units_needed'] = array_unique($stats['units_needed']);
            $stats['unique_categories'] = count($stats['categories_needed']);
            $stats['unique_units'] = count($stats['units_needed']);

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching API stats: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * بدء عملية الاستيراد
     */
    public function startImport(): JsonResponse
    {
        try {
            // جلب البيانات من الـ API
            $response = Http::timeout(60)->get('https://albayan.live/ApiWithAmeen/public/api/HemammaterialsData');

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'فشل في جلب البيانات من الـ API'
                ]);
            }

            $apiItems = $response->json();

            if (empty($apiItems)) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد بيانات في الـ API'
                ]);
            }

            // حفظ البيانات في الـ session للمعالجة التدريجية
            session([
                'import_data' => $apiItems,
                'import_progress' => [
                    'total' => count($apiItems),
                    'processed' => 0,
                    'imported' => 0,
                    'updated' => 0,
                    'skipped' => 0,
                    'errors' => 0,
                    'current_batch' => 0,
                ]
            ]);

            return response()->json([
                'success' => true,
                'total_items' => count($apiItems),
                'message' => 'تم تحضير البيانات للاستيراد'
            ]);

        } catch (\Exception $e) {
            Log::error('Error starting import: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء بدء الاستيراد: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معالجة دفعة من المنتجات
     */
    public function processBatch(): JsonResponse
    {
        try {
            $importData = session('import_data');
            $progress = session('import_progress');

            if (!$importData || !$progress) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد بيانات للمعالجة'
                ]);
            }

            $batchSize = 10; // معالجة 10 عناصر في كل دفعة
            $startIndex = $progress['current_batch'] * $batchSize;
            $batch = array_slice($importData, $startIndex, $batchSize);

            if (empty($batch)) {
                // انتهت المعالجة
                session()->forget(['import_data', 'import_progress']);
                return response()->json([
                    'success' => true,
                    'completed' => true,
                    'progress' => $progress
                ]);
            }

            $moduleId = config('module.current_module_id');
            $batchResults = [
                'imported' => 0,
                'updated' => 0,
                'skipped' => 0,
                'errors' => 0,
            ];

            DB::beginTransaction();

            foreach ($batch as $apiItem) {
                try {
                    $result = $this->processItem($apiItem, $moduleId);
                    $batchResults[$result]++;
                } catch (\Exception $e) {
                    Log::error('Error processing item: ' . $e->getMessage(), ['item' => $apiItem]);
                    $batchResults['errors']++;
                }
            }

            DB::commit();

            // تحديث التقدم
            $progress['processed'] += count($batch);
            $progress['imported'] += $batchResults['imported'];
            $progress['updated'] += $batchResults['updated'];
            $progress['skipped'] += $batchResults['skipped'];
            $progress['errors'] += $batchResults['errors'];
            $progress['current_batch']++;

            session(['import_progress' => $progress]);

            return response()->json([
                'success' => true,
                'completed' => false,
                'progress' => $progress,
                'batch_results' => $batchResults
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error processing batch: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الدفعة: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * معالجة عنصر واحد
     */
    private function processItem(array $apiItem, int $moduleId): string
    {
        $itemName = trim($apiItem['Name'] ?? '');
        $itemGuid = $apiItem['GUID'] ?? '';

        if (empty($itemName) || empty($itemGuid)) {
            return 'skipped';
        }

        // التحقق من وجود المنتج
        $existingItem = Item::where('guid', $itemGuid)->first();

        if ($existingItem) {
            // تحديث المنتج الموجود
            $this->updateExistingItem($existingItem, $apiItem);
            return 'updated';
        } else {
            // إنشاء منتج جديد
            $this->createNewItem($apiItem, $moduleId);
            return 'imported';
        }
    }

    /**
     * إنشاء منتج جديد
     */
    private function createNewItem(array $apiItem, int $moduleId): void
    {
        $arabicName = trim($apiItem['Name'] ?? '');
        $englishName = trim($apiItem['LatinName'] ?? '');
        $defaultName = !empty($arabicName) ? $arabicName : (!empty($englishName) ? $englishName : 'Unknown');

        // البحث عن القسم
        $categoryId = $this->getCategoryIdByGuid($apiItem['GroupGUID'] ?? '');

        // البحث عن الوحدة
        $unitId = $this->getUnitIdByName(trim($apiItem['Unity'] ?? ''));

        // إنشاء المنتج
        $item = new Item();
        $item->name = $defaultName;
        $item->description = trim($apiItem['Spec'] ?? '') ?: 'لا يوجد وصف';
        $item->image = 'def.png';
        $item->category_id = $categoryId;
        $item->price = floatval($apiItem['EndUser'] ?? 0); // السعر من حقل EndUser
        $item->discount = 0;
        $item->discount_type = 'percent';
        $item->available_time_starts = '00:00:00';
        $item->available_time_ends = '23:59:59';
        $item->status = 1;
        $item->store_id = 1; // يجب تحديد store_id مناسب
        $item->module_id = $moduleId;
        $item->unit_id = $unitId;
        $item->stock = intval($apiItem['Qty'] ?? 0);
        $item->guid = $apiItem['GUID'];
        $item->code = $apiItem['Code'] ?? '';
        $item->barcode = $apiItem['BarCode'] ?? '';
        $item->veg = 1;
        $item->recommended = 0;
        $item->organic = 0;
        $item->is_halal = 1;
        $item->save();

        // إضافة الترجمات
        $this->addItemTranslations($item, $arabicName, $englishName);

        // معالجة الإضافات إذا وجدت
        if (!empty($apiItem['Rest']) && is_array($apiItem['Rest'])) {
            $this->processItemAddons($item, $apiItem['Rest']);
        }
    }

    /**
     * تحديث منتج موجود
     */
    private function updateExistingItem(Item $item, array $apiItem): void
    {
        $arabicName = trim($apiItem['Name'] ?? '');
        $englishName = trim($apiItem['LatinName'] ?? '');
        $defaultName = !empty($arabicName) ? $arabicName : (!empty($englishName) ? $englishName : $item->name);

        // تحديث البيانات الأساسية
        $item->name = $defaultName;
        $item->description = trim($apiItem['Spec'] ?? '') ?: $item->description;
        $item->price = floatval($apiItem['EndUser'] ?? $item->price); // السعر من حقل EndUser
        $item->stock = intval($apiItem['Qty'] ?? $item->stock);
        $item->code = $apiItem['Code'] ?? $item->code;
        $item->barcode = $apiItem['BarCode'] ?? $item->barcode;

        // تحديث القسم إذا تغير
        $categoryId = $this->getCategoryIdByGuid($apiItem['GroupGUID'] ?? '');
        if ($categoryId) {
            $item->category_id = $categoryId;
        }

        // تحديث الوحدة إذا تغيرت
        $unitId = $this->getUnitIdByName(trim($apiItem['Unity'] ?? ''));
        if ($unitId) {
            $item->unit_id = $unitId;
        }

        $item->save();

        // تحديث الترجمات
        $this->updateItemTranslations($item, $arabicName, $englishName);

        // معالجة الإضافات
        if (!empty($apiItem['Rest']) && is_array($apiItem['Rest'])) {
            $this->processItemAddons($item, $apiItem['Rest']);
        }
    }

    /**
     * البحث عن معرف القسم بواسطة GUID
     */
    private function getCategoryIdByGuid(string $guid): ?int
    {
        if (empty($guid) || $guid === '00000000-0000-0000-0000-000000000000') {
            return null;
        }

        $category = $this->categoryRepo->getFirstWhere(['guid' => $guid]);
        return $category ? $category->id : null;
    }

    /**
     * البحث عن معرف الوحدة بواسطة الاسم
     */
    private function getUnitIdByName(string $unitName): ?int
    {
        if (empty($unitName)) {
            return null;
        }

        $unit = $this->unitRepo->getFirstWhere(['unit' => $unitName]);
        return $unit ? $unit->id : null;
    }

    /**
     * إضافة ترجمات للمنتج الجديد
     */
    private function addItemTranslations(Item $item, string $arabicName, string $englishName): void
    {
        // إضافة الترجمة العربية
        if (!empty($arabicName)) {
            Translation::updateOrCreate([
                'translationable_type' => 'App\Models\Item',
                'translationable_id' => $item->id,
                'locale' => 'ar',
                'key' => 'name',
            ], [
                'value' => $arabicName,
            ]);
        }

        // إضافة الترجمة الإنجليزية
        if (!empty($englishName) && $englishName !== $arabicName) {
            Translation::updateOrCreate([
                'translationable_type' => 'App\Models\Item',
                'translationable_id' => $item->id,
                'locale' => 'en',
                'key' => 'name',
            ], [
                'value' => $englishName,
            ]);
        }
    }

    /**
     * تحديث ترجمات المنتج الموجود
     */
    private function updateItemTranslations(Item $item, string $arabicName, string $englishName): void
    {
        $this->addItemTranslations($item, $arabicName, $englishName);
    }

    /**
     * معالجة إضافات المنتج
     */
    private function processItemAddons(Item $item, array $restItems): void
    {
        foreach ($restItems as $restItem) {
            $addonName = trim($restItem['Name'] ?? '');
            $addonPrice = floatval($restItem['EndUser'] ?? 0); // سعر الإضافة من حقل EndUser

            if (empty($addonName)) {
                continue;
            }

            // التحقق من وجود الإضافة
            $existingAddon = AddOn::where('name', $addonName)
                ->where('store_id', $item->store_id)
                ->first();

            if (!$existingAddon) {
                // إنشاء إضافة جديدة
                $addon = new AddOn();
                $addon->name = $addonName;
                $addon->price = $addonPrice; // سعر الإضافة من EndUser
                $addon->store_id = $item->store_id;
                $addon->status = 1;
                $addon->save();

                // إضافة ترجمات للإضافة
                $this->addAddonTranslations($addon, $addonName, trim($restItem['LatinName'] ?? ''));
            } else {
                // تحديث السعر إذا تغير
                if ($existingAddon->price != $addonPrice) {
                    $existingAddon->price = $addonPrice; // تحديث السعر من EndUser
                    $existingAddon->save();
                }
            }
        }
    }

    /**
     * إضافة ترجمات للإضافة
     */
    private function addAddonTranslations(AddOn $addon, string $arabicName, string $englishName): void
    {
        // إضافة الترجمة العربية
        if (!empty($arabicName)) {
            Translation::updateOrCreate([
                'translationable_type' => 'App\Models\AddOn',
                'translationable_id' => $addon->id,
                'locale' => 'ar',
                'key' => 'name',
            ], [
                'value' => $arabicName,
            ]);
        }

        // إضافة الترجمة الإنجليزية
        if (!empty($englishName) && $englishName !== $arabicName) {
            Translation::updateOrCreate([
                'translationable_type' => 'App\Models\AddOn',
                'translationable_id' => $addon->id,
                'locale' => 'en',
                'key' => 'name',
            ], [
                'value' => $englishName,
            ]);
        }
    }
}

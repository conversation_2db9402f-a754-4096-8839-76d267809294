<?php

namespace App\Http\Controllers\Admin\Item;

use App\Contracts\Repositories\CategoryRepositoryInterface;
use App\Contracts\Repositories\TranslationRepositoryInterface;
use App\Enums\ExportFileNames\Admin\Category;
use App\Enums\ViewPaths\Admin\Category as CategoryViewPath;
use App\Exports\CategoryExport;
use App\Http\Controllers\BaseController;
use App\Http\Requests\Admin\CategoryAddRequest;
use App\Http\Requests\Admin\CategoryBulkExportRequest;
use App\Http\Requests\Admin\CategoryBulkImportRequest;
use App\Http\Requests\Admin\CategoryUpdateRequest;
use App\Services\CategoryService;
use App\Traits\ImportExportTrait;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use OpenSpout\Common\Exception\InvalidArgumentException;
use OpenSpout\Common\Exception\IOException;
use OpenSpout\Common\Exception\UnsupportedTypeException;
use OpenSpout\Writer\Exception\WriterNotOpenedException;
use Rap2hpoutre\FastExcel\FastExcel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CategoryController extends BaseController
{
    use ImportExportTrait;

    public function __construct(
        protected CategoryRepositoryInterface $categoryRepo,
        protected CategoryService $categoryService,
        protected TranslationRepositoryInterface $translationRepo
    ) {
    }

    public function index(?Request $request): View|Collection|LengthAwarePaginator|null
    {
        return $this->getCategoryView($request);
    }

    private function getCategoryView(Request $request): View
    {
        $categories = $this->categoryRepo->getListWhere(
            searchValue: $request['search'],
            filters: ['position' => $request['position']],
            relations: ['module'],
            dataLimit: config('default_pagination')
        );

        $mainCategories = $this->categoryRepo->getMainList(
            filters: ['position' => 0],
            relations: ['module'],
        );

        $language = getWebConfig('language');
        $defaultLang = str_replace('_', '-', app()->getLocale());

        return view($this->categoryService->getViewByPosition($request['position']), compact('categories', 'language', 'defaultLang', 'mainCategories'));
    }

    public function add(CategoryAddRequest $request): RedirectResponse
    {
        $parentCategory = $this->categoryRepo->getFirstWhere(params: ['id' => $request['parent_id']]);
        $category = $this->categoryRepo->add(
            data: $this->categoryService->getAddData(
                request: $request,
                parentCategory: $parentCategory
            )
        );
        $this->translationRepo->addByModel(request: $request, model: $category, modelPath: 'App\Models\Category', attribute: 'name');
        Toastr::success($request['position'] == 0 ? translate('messages.category_added_successfully') : translate('messages.Sub_category_added_successfully'));

        return back();
    }

    public function getUpdateView(string|int $id): View
    {
        $category = $this->categoryRepo->getFirstWithoutGlobalScopeWhere(params: ['id' => $id]);
        $language = getWebConfig('language');
        $defaultLang = str_replace('_', '-', app()->getLocale());

        return view(CategoryViewPath::UPDATE['view'], compact('category', 'language', 'defaultLang'));
    }

    public function updateStatus(Request $request): RedirectResponse
    {
        $this->categoryRepo->update(id: $request['id'], data: ['status' => $request['status']]);
        Toastr::success(translate('messages.category_status_updated'));

        return back();
    }

    public function updateFeatured(Request $request): RedirectResponse
    {
        $this->categoryRepo->update(id: $request['id'], data: ['featured' => $request['featured']]);
        Toastr::success(translate('messages.category_featured_updated'));

        return back();
    }

    public function update(CategoryUpdateRequest $request, string|int $id): RedirectResponse
    {
        $mainCategory = $this->categoryRepo->getFirstWhere(params: ['id' => $id]);
        $category = $this->categoryRepo->update(id: $id, data: $this->categoryService->getUpdateData(request: $request, object: $mainCategory));
        $this->translationRepo->updateByModel(request: $request, model: $category, modelPath: 'App\Models\Category', attribute: 'name');
        Toastr::success($category['position'] == 0 ? translate('messages.category_updated_successfully') : translate('messages.Sub_category_updated_successfully'));

        return redirect()->route('admin.category.add', ['position' => $mainCategory->position]);
    }

    public function delete(Request $request): RedirectResponse
    {
        if ($this->categoryRepo->delete(id: $request['id'])) {
            Toastr::success('Category removed!');
        } else {
            Toastr::warning(translate('messages.remove_sub_categories_first'));
        }

        return back();
    }

    public function getNameList(Request $request): JsonResponse
    {
        $data = $this->categoryRepo->getNameList(request: $request, dataLimit: 8);
        $data[] = (object) ['id' => 'all', 'text' => 'All'];

        return response()->json($data);
    }

    public function updatePriority(Request $request): RedirectResponse
    {
        $this->categoryRepo->update(id: $request['category'], data: ['priority' => $request['priority']]);
        Toastr::success(translate('messages.category_priority_updated successfully'));

        return back();
    }

    public function getBulkImportView(): View
    {
        return view(CategoryViewPath::BULK_IMPORT['view']);
    }

    public function importBulkData(CategoryBulkImportRequest $request): RedirectResponse
    {
        $data = $this->categoryService->getImportData(request: $request);

        if (array_key_exists('flag', $data) && $data['flag'] == 'wrong_format') {
            Toastr::error(translate('messages.you_have_uploaded_a_wrong_format_file'));

            return back();
        }

        if (array_key_exists('flag', $data) && $data['flag'] == 'required_fields') {
            Toastr::error(translate('messages.please_fill_all_required_fields'));

            return back();
        }

        try {
            DB::beginTransaction();
            $this->categoryRepo->addByChunk(data: $data);
            DB::commit();
        } catch (\Exception) {
            DB::rollBack();
            Toastr::error(translate('messages.failed_to_import_data'));

            return back();
        }

        Toastr::success(translate('messages.category_imported_successfully', ['count' => count($data)]));

        return back();
    }

    public function updateBulkData(CategoryBulkImportRequest $request): RedirectResponse
    {
        $data = $this->categoryService->getImportData(request: $request, toAdd: false);

        if (array_key_exists('flag', $data) && $data['flag'] == 'wrong_format') {
            Toastr::error(translate('messages.you_have_uploaded_a_wrong_format_file'));

            return back();
        }

        if (array_key_exists('flag', $data) && $data['flag'] == 'required_fields') {
            Toastr::error(translate('messages.please_fill_all_required_fields'));

            return back();
        }

        try {
            DB::beginTransaction();
            $this->categoryRepo->updateByChunk(data: $data);
            DB::commit();
        } catch (\Exception) {
            DB::rollBack();
            Toastr::error(translate('messages.failed_to_import_data'));

            return back();
        }

        Toastr::success(translate('messages.category_updated_successfully', ['count' => count($data)]));

        return back();
    }

    public function getBulkExportView(): View
    {
        return view(CategoryViewPath::BULK_EXPORT['view']);
    }

    /**
     * @throws IOException
     * @throws WriterNotOpenedException
     * @throws UnsupportedTypeException
     * @throws InvalidArgumentException
     */
    public function exportBulkData(CategoryBulkExportRequest $request): StreamedResponse|string
    {
        $categories = $this->categoryRepo->getBulkExportList(request: $request);

        return (new FastExcel($this->categoryService->getExportData(collection: $this->exportGenerator(data: $categories))))->download(Category::EXPORT_XLSX);
    }

    public function exportList(Request $request): BinaryFileResponse
    {
        $categories = $this->categoryRepo->getExportList(request: $request);
        $data = [
            'data' => $categories,
            'search' => $request['search'] ?? null,
        ];

        if ($request['type'] == 'csv') {
            return Excel::download(new CategoryExport($data), Category::EXPORT_CSV);
        }

        return Excel::download(new CategoryExport($data), Category::EXPORT_XLSX);
    }

    public function importFromApi(): RedirectResponse
    {
        try {
            // استدعاء API لجلب الأقسام
            // يتم تخزين Name كاسم عربي و LatinName كاسم إنجليزي
            // مع تحديث الأقسام الموجودة بالأسماء الجديدة
            $response = Http::timeout(30)->get('https://albayan.live/ApiWithAmeen/public/api/HemamGroupsData');

            if (!$response->successful()) {
                Toastr::error(translate('messages.failed_to_fetch_data_from_api'));

                return back();
            }

            $apiCategories = $response->json();

            if (empty($apiCategories)) {
                Toastr::warning(translate('messages.no_data_found_in_api'));

                return back();
            }

            $importedCount = 0;
            $updatedCount = 0;
            $skippedCount = 0;
            $moduleId = config('module.current_module_id');

            DB::beginTransaction();

            // ترتيب الأقسام: الآباء أولاً ثم الأبناء
            $sortedCategories = $this->sortCategoriesByHierarchy($apiCategories);

            foreach ($sortedCategories as $apiCategory) {
                // التحقق من وجود القسم بناءً على GUID
                $existingCategory = $this->categoryRepo->getFirstWhere(['guid' => $apiCategory['GUID']]);

                if ($existingCategory) {
                    // تحديث الأسماء والترجمات للقسم الموجود
                    $this->updateExistingCategoryFromApi($existingCategory, $apiCategory);
                    ++$updatedCount;
                    continue;
                }

                // معالجة الأسماء من الـ API
                $arabicName = trim($apiCategory['Name'] ?? '');
                $englishName = trim($apiCategory['LatinName'] ?? '');

                // تحديد الاسم الافتراضي (العربي أولاً، ثم الإنجليزي)
                $defaultName = !empty($arabicName) ? $arabicName : (!empty($englishName) ? $englishName : 'Unknown');

                // إنشاء القسم الجديد
                $categoryData = [
                    'guid' => $apiCategory['GUID'],
                    'name' => $defaultName,
                    'image' => 'def.png',
                    'parent_id' => $this->getParentIdByGuid($apiCategory['ParentGUID']),
                    'position' => $apiCategory['ParentGUID'] === '00000000-0000-0000-0000-000000000000' ? 0 : 1,
                    'status' => 1,
                    'priority' => 0,
                    'module_id' => $moduleId,
                ];

                $category = $this->categoryRepo->add($categoryData);

                // إضافة الترجمات
                $this->addCategoryTranslationsFromApi($category, $arabicName, $englishName);

                ++$importedCount;
            }

            DB::commit();

            $message = "تم استيراد {$importedCount} قسم جديد، وتحديث {$updatedCount} قسم موجود";
            if ($skippedCount > 0) {
                $message .= "، وتخطي {$skippedCount} قسم";
            }

            Toastr::success($message);
        } catch (\Exception $e) {
            DB::rollBack();
            Toastr::error(translate('messages.failed_to_import_categories').': '.$e->getMessage());
        }

        return back();
    }

    public function cleanupDuplicateCategories(): RedirectResponse
    {
        try {
            DB::beginTransaction();

            // حذف الترجمات المكررة
            DB::statement("
                DELETE t1 FROM translations t1
                INNER JOIN translations t2
                WHERE t1.id > t2.id
                AND t1.translationable_type = 'App\\\\Models\\\\Category'
                AND t1.translationable_id = t2.translationable_id
                AND t1.locale = t2.locale
                AND t1.key = t2.key
            ");

            // حذف الأقسام المكررة بناءً على GUID
            $duplicateCategories = DB::select("
                SELECT guid, MIN(id) as keep_id, GROUP_CONCAT(id) as all_ids
                FROM categories
                WHERE guid IS NOT NULL AND guid != ''
                GROUP BY guid
                HAVING COUNT(*) > 1
            ");

            foreach ($duplicateCategories as $duplicate) {
                $idsToDelete = explode(',', $duplicate->all_ids);
                $keepId = $duplicate->keep_id;

                // إزالة ID المحفوظ من قائمة الحذف
                $idsToDelete = array_filter($idsToDelete, function ($id) use ($keepId) {
                    return $id != $keepId;
                });

                if (!empty($idsToDelete)) {
                    // حذف الترجمات للأقسام المكررة
                    DB::table('translations')
                        ->where('translationable_type', 'App\\Models\\Category')
                        ->whereIn('translationable_id', $idsToDelete)
                        ->delete();

                    // حذف الأقسام المكررة
                    DB::table('categories')->whereIn('id', $idsToDelete)->delete();
                }
            }

            DB::commit();
            Toastr::success('تم تنظيف البيانات المكررة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Toastr::error('فشل في تنظيف البيانات: '.$e->getMessage());
        }

        return back();
    }

    public function fixCategoryNames(): RedirectResponse
    {
        try {
            DB::beginTransaction();

            // الحصول على جميع الأقسام مع ترجماتها
            $categories = DB::select("
                SELECT c.id, c.name as current_name, c.guid,
                       t_ar.value as arabic_name,
                       t_en.value as english_name
                FROM categories c
                LEFT JOIN translations t_ar ON c.id = t_ar.translationable_id
                    AND t_ar.translationable_type = 'App\\\\Models\\\\Category'
                    AND t_ar.locale = 'ar' AND t_ar.key = 'name'
                LEFT JOIN translations t_en ON c.id = t_en.translationable_id
                    AND t_en.translationable_type = 'App\\\\Models\\\\Category'
                    AND t_en.locale = 'en' AND t_en.key = 'name'
                WHERE c.guid IS NOT NULL AND c.guid != ''
            ");

            $fixedCount = 0;

            foreach ($categories as $category) {
                $shouldUpdate = false;
                $newName = $category->current_name;

                // إذا كان الاسم الحالي إنجليزي والترجمة العربية موجودة
                if (!empty($category->arabic_name)
                    && !preg_match('/[\x{0600}-\x{06FF}]/u', $category->current_name)
                    && preg_match('/[\x{0600}-\x{06FF}]/u', $category->arabic_name)) {
                    $newName = $category->arabic_name;
                    $shouldUpdate = true;
                }

                if ($shouldUpdate) {
                    DB::table('categories')
                        ->where('id', $category->id)
                        ->update(['name' => $newName]);

                    ++$fixedCount;
                }
            }

            DB::commit();
            Toastr::success("تم إصلاح {$fixedCount} قسم بنجاح");
        } catch (\Exception $e) {
            DB::rollBack();
            Toastr::error('فشل في إصلاح الأسماء: '.$e->getMessage());
        }

        return back();
    }

    private function sortCategoriesByHierarchy(array $categories): array
    {
        $parentCategories = [];
        $childCategories = [];

        // فصل الأقسام الآباء عن الأبناء
        foreach ($categories as $category) {
            if ($category['ParentGUID'] === '00000000-0000-0000-0000-000000000000' || empty($category['ParentGUID'])) {
                $parentCategories[] = $category;
            } else {
                $childCategories[] = $category;
            }
        }

        // دمج الآباء أولاً ثم الأبناء
        return array_merge($parentCategories, $childCategories);
    }

    private function updateExistingCategoryFromApi($category, array $apiCategory): void
    {
        // معالجة الأسماء من الـ API
        $arabicName = trim($apiCategory['Name'] ?? '');
        $englishName = trim($apiCategory['LatinName'] ?? '');

        // تحديد الاسم الافتراضي (العربي أولاً، ثم الإنجليزي)
        $defaultName = !empty($arabicName) ? $arabicName : (!empty($englishName) ? $englishName : $category->name);

        // تحديث الاسم الافتراضي للقسم إذا كان مختلفاً
        if ($category->name !== $defaultName) {
            $this->categoryRepo->update($category->id, ['name' => $defaultName]);
        }

        // تحديث الترجمات
        $this->updateCategoryTranslationsFromApi($category, $arabicName, $englishName);
    }

    private function updateCategoryTranslationsFromApi($category, string $arabicName, string $englishName): void
    {
        // تحديث الترجمة العربية
        if (!empty($arabicName)) {
            \App\Models\Translation::updateOrCreate([
                'translationable_type' => 'App\Models\Category',
                'translationable_id' => $category->id,
                'locale' => 'ar',
                'key' => 'name',
            ], [
                'value' => $arabicName,
            ]);
        }

        // تحديث الترجمة الإنجليزية إذا كانت موجودة ومختلفة عن العربية
        if (!empty($englishName) && $englishName !== $arabicName) {
            \App\Models\Translation::updateOrCreate([
                'translationable_type' => 'App\Models\Category',
                'translationable_id' => $category->id,
                'locale' => 'en',
                'key' => 'name',
            ], [
                'value' => $englishName,
            ]);
        }
    }

    private function parseApiCategoryName(string $name): array
    {
        // إزالة علامات الاقتباس الإضافية إن وجدت
        $name = trim($name, '"');

        // التحقق من وجود نمط "English: Arabic" أو "English: العربية"
        if (strpos($name, ':') !== false) {
            $parts = explode(':', $name, 2);
            $english = trim($parts[0]);
            $arabic = trim($parts[1]);

            return [
                'english' => $english,
                'arabic' => $arabic,
            ];
        }

        // إذا لم يوجد فاصل، نفترض أن النص باللغة العربية
        // نتحقق إذا كان النص يحتوي على أحرف عربية
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $name)) {
            return [
                'english' => $name, // نستخدم نفس النص كإنجليزي مؤقتاً
                'arabic' => $name,
            ];
        } else {
            return [
                'english' => $name,
                'arabic' => $name, // نستخدم نفس النص كعربي مؤقتاً
            ];
        }
    }

    private function getParentIdByGuid(string $parentGuid): int
    {
        // إذا كان GUID فارغ أو يحتوي على أصفار فقط، فهو قسم رئيسي
        if ($parentGuid === '00000000-0000-0000-0000-000000000000' || empty($parentGuid)) {
            return 0;
        }

        // البحث عن القسم الأب بناءً على GUID
        $parentCategory = $this->categoryRepo->getFirstWhere(['guid' => $parentGuid]);

        return $parentCategory ? $parentCategory->id : 0;
    }

    private function addCategoryTranslationsFromApi($category, string $arabicName, string $englishName): void
    {
        // إضافة الترجمة العربية إذا كانت موجودة
        if (!empty($arabicName)) {
            \App\Models\Translation::updateOrCreate([
                'translationable_type' => 'App\Models\Category',
                'translationable_id' => $category->id,
                'locale' => 'ar',
                'key' => 'name',
            ], [
                'value' => $arabicName,
            ]);
        }

        // إضافة الترجمة الإنجليزية إذا كانت موجودة ومختلفة عن العربية
        if (!empty($englishName) && $englishName !== $arabicName) {
            \App\Models\Translation::updateOrCreate([
                'translationable_type' => 'App\Models\Category',
                'translationable_id' => $category->id,
                'locale' => 'en',
                'key' => 'name',
            ], [
                'value' => $englishName,
            ]);
        }
    }

    private function addCategoryTranslations($category, array $names): void
    {
        // تحضير البيانات للترجمات
        $translationData = [];

        // إضافة الترجمة العربية
        if (!empty($names['arabic'])) {
            $translationData['ar'] = $names['arabic'];
        }

        // إضافة الترجمة الإنجليزية إذا كانت مختلفة
        if (!empty($names['english']) && $names['english'] !== $names['arabic']) {
            $translationData['en'] = $names['english'];
        }

        // إضافة الترجمات إذا كان هناك بيانات
        if (!empty($translationData)) {
            $request = new Request([
                'name' => array_values($translationData),
                'lang' => array_keys($translationData),
            ]);

            $this->translationRepo->addByModel(
                request: $request,
                model: $category,
                modelPath: 'App\Models\Category',
                attribute: 'name'
            );
        }
    }
}

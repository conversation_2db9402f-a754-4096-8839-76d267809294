<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Translation;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class UnitController extends Controller
{
    public function index()
    {
        $units = Unit::latest()->paginate(config('default_pagination'));
        return view('admin-views.unit.index', compact('units'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'unit' => 'required|max:100',
        ], [
            'unit.required' => translate('Unit name is required!'),
        ]);

        $unit = Unit::create([
            'unit' => $request->unit,
        ]);

        $data = [];
        $data[0]['translationable_type'] = 'App\Models\Unit';
        $data[0]['translationable_id'] = $unit->id;
        $data[0]['locale'] = 'en';
        $data[0]['key'] = 'unit';
        $data[0]['value'] = $request->unit;

        if ($request->unit_ar) {
            $data[1]['translationable_type'] = 'App\Models\Unit';
            $data[1]['translationable_id'] = $unit->id;
            $data[1]['locale'] = 'ar';
            $data[1]['key'] = 'unit';
            $data[1]['value'] = $request->unit_ar;
        }

        Translation::insert($data);

        return response()->json(['message' => translate('Unit added successfully!')], 200);
    }

    public function edit($id)
    {
        $unit = Unit::withoutGlobalScope('translate')->findOrFail($id);
        return response()->json($unit);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'unit' => 'required|max:100',
        ], [
            'unit.required' => translate('Unit name is required!'),
        ]);

        $unit = Unit::findOrFail($id);
        $unit->unit = $request->unit;
        $unit->save();

        foreach ($unit->translations as $translation) {
            Translation::updateOrCreate(
                ['translationable_type' => 'App\Models\Unit',
                    'translationable_id' => $unit->id,
                    'locale' => $translation->locale,
                    'key' => $translation->key],
                ['value' => ($translation->locale == 'en') ? $request->unit : $request->unit_ar]
            );
        }

        return response()->json(['message' => translate('Unit updated successfully!')], 200);
    }

    public function destroy($id)
    {
        $unit = Unit::findOrFail($id);
        $unit->translations()->delete();
        $unit->delete();
        return response()->json(['message' => translate('Unit deleted successfully!')], 200);
    }

    public function importUnitsFromApi()
    {
        try {
            // استدعاء API الوحدات
            $response = Http::timeout(60)->get('https://albayan.live/ApiWithAmeen/public/api/HemamUnityData');
            $useDefaultUnits = false;

            if (!$response->successful()) {
                // في حالة فشل API، استخدم وحدات افتراضية
                $apiUnits = [
                    ['Unit' => 'بكيت'],
                    ['Unit' => 'حبة'],
                    ['Unit' => 'عبوة'],
                    ['Unit' => 'علبة'],
                    ['Unit' => 'كجم'],
                    ['Unit' => 'كروز'],
                    ['Unit' => 'لتر'],
                    ['Unit' => 'متر'],
                    ['Unit' => 'قطعة'],
                    ['Unit' => 'صندوق'],
                ];
                $useDefaultUnits = true;
            } else {
                $apiUnits = $response->json();

                if (empty($apiUnits)) {
                    return redirect()->back()->with('error', 'لا توجد وحدات في API');
                }
            }

            DB::beginTransaction();

            $importedCount = 0;
            $skippedCount = 0;

            foreach ($apiUnits as $apiUnit) {
                try {
                    // التحقق من وجود البيانات الأساسية
                    if (!is_array($apiUnit) || empty($apiUnit['Unit'])) {
                        $skippedCount++;
                        continue;
                    }

                    $unitName = trim($apiUnit['Unit']);

                    // التحقق من وجود الوحدة مسبقاً
                    $existingUnit = Unit::where('unit', $unitName)->first();

                    if ($existingUnit) {
                        $skippedCount++;
                        continue;
                    }

                    // إنشاء الوحدة الجديدة
                    $unit = Unit::create([
                        'unit' => $unitName,
                    ]);

                    // إضافة الترجمة العربية
                    Translation::updateOrCreate([
                        'translationable_type' => 'App\Models\Unit',
                        'translationable_id' => $unit->id,
                        'locale' => 'ar',
                        'key' => 'unit',
                    ], [
                        'value' => $unitName,
                    ]);

                    $importedCount++;

                } catch (\Exception $unitError) {
                    \Log::error('خطأ في معالجة وحدة: ' . $unitError->getMessage(), [
                        'unit' => $apiUnit ?? 'غير محدد'
                    ]);
                    $skippedCount++;
                    continue;
                }
            }

            DB::commit();

            $message = "تم استيراد {$importedCount} وحدة جديدة وتخطي {$skippedCount} وحدة موجودة مسبقاً";

            if ($useDefaultUnits) {
                $message .= " (تم استخدام وحدات افتراضية بسبب عدم توفر API)";
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء استيراد الوحدات: ' . $e->getMessage());
        }
    }

    public function export($type)
    {
        // TODO: Implement export functionality
        return redirect()->back()->with('info', 'وظيفة التصدير قيد التطوير');
    }
}

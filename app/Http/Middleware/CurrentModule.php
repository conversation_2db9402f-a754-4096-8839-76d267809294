<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Request;
use App\Models\Module;

class CurrentModule
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // تحديد الـ module من الـ request أو الـ session
        if (request()->get('module_id')) {
            session()->put('current_module', request()->get('module_id'));
            Config::set('module.current_module_id', request()->get('module_id'));
        } else {
            $sessionModule = session()->get('current_module');
            Config::set('module.current_module_id', $sessionModule);
        }

        $module_id = Config::get('module.current_module_id');
        $module_id = is_array($module_id) ? null : $module_id;

        // إذا لم يكن هناك module محدد، استخدم أول module نشط
        if (!$module_id) {
            $module = Module::with('translations')->where('status', 1)->first();
            if ($module) {
                $module_id = $module->id;
                session()->put('current_module', $module_id);
                Config::set('module.current_module_id', $module_id);
            }
        } else {
            $module = Module::with('translations')->find($module_id);
        }

        // إذا لم يتم العثور على الـ module، استخدم أول module نشط
        if (!$module) {
            $module = Module::with('translations')->where('status', 1)->first();
            if ($module) {
                session()->put('current_module', $module->id);
                Config::set('module.current_module_id', $module->id);
            }
        }

        if ($module) {
            Config::set('module.current_module_id', $module->id);
            Config::set('module.current_module_type', $module->module_type);
            Config::set('module.current_module_name', $module->module_name);
            Config::set('module.current_module_data', $module);
        } else {
            Config::set('module.current_module_id', null);
            Config::set('module.current_module_type', 'settings');
            Config::set('module.current_module_data', null);
        }

        // إعدادات خاصة لصفحات معينة
        if (Request::is('admin/users*')) {
            Config::set('module.current_module_id', null);
            Config::set('module.current_module_type', 'users');
        }
        if (Request::is('admin/transactions*')) {
            Config::set('module.current_module_id', null);
            Config::set('module.current_module_type', 'transactions');
        }
        if (Request::is('admin/dispatch*')) {
            Config::set('module.current_module_id', null);
            Config::set('module.current_module_type', 'dispatch');
        }
        if (Request::is('admin/business-settings/*')) {
            Config::set('module.current_module_id', null);
            Config::set('module.current_module_type', 'settings');
        }

        return $next($request);
    }
}

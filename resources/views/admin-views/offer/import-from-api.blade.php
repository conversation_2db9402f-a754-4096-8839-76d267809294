@extends('layouts.admin.app')

@section('title', translate('messages.import_offers_from_api'))

@push('css_or_js')
    <style>
        .import-card {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .import-card:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .import-icon {
            font-size: 48px;
            color: #007bff;
            margin-bottom: 20px;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        .results-container {
            display: none;
            margin-top: 20px;
        }
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .result-success { background-color: #d4edda; color: #155724; }
        .result-warning { background-color: #fff3cd; color: #856404; }
        .result-error { background-color: #f8d7da; color: #721c24; }
    </style>
@endpush

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">
                        <i class="tio-download"></i>
                        {{translate('messages.import_offers_from_api')}}
                    </h1>
                    <p class="page-header-text">{{translate('messages.import_special_offers_from_external_api')}}</p>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="tio-gift"></i>
                            {{translate('messages.special_offers_import')}}
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Import Section -->
                        <div class="import-card" id="importCard">
                            <div class="import-icon">
                                <i class="tio-cloud-download"></i>
                            </div>
                            <h4>{{translate('messages.import_special_offers')}}</h4>
                            <p class="text-muted">{{translate('messages.click_button_below_to_import_offers_from_api')}}</p>
                            
                            <button type="button" class="btn btn-primary btn-lg" id="importBtn">
                                <i class="tio-download"></i>
                                {{translate('messages.start_import')}}
                            </button>
                        </div>

                        <!-- Progress Section -->
                        <div class="progress-container" id="progressContainer">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">{{translate('messages.loading')}}</span>
                                </div>
                                <h5 class="mt-3">{{translate('messages.importing_offers_please_wait')}}</h5>
                                <p class="text-muted">{{translate('messages.this_may_take_few_minutes')}}</p>
                            </div>
                        </div>

                        <!-- Results Section -->
                        <div class="results-container" id="resultsContainer">
                            <h5>{{translate('messages.import_results')}}</h5>
                            <div id="resultsContent"></div>
                            
                            <div class="mt-3">
                                <button type="button" class="btn btn-secondary" id="importAgainBtn">
                                    <i class="tio-refresh"></i>
                                    {{translate('messages.import_again')}}
                                </button>
                                <a href="{{route('admin.coupon.add-new')}}" class="btn btn-primary">
                                    <i class="tio-gift"></i>
                                    {{translate('messages.view_offers')}}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Cards -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="tio-api text-primary" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">{{translate('messages.api_source')}}</h6>
                        <small class="text-muted">albayan.live API</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="tio-gift text-success" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">{{translate('messages.offer_types')}}</h6>
                        <small class="text-muted">{{translate('messages.special_discounts_and_promotions')}}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="tio-sync text-info" style="font-size: 2rem;"></i>
                        <h6 class="mt-2">{{translate('messages.auto_sync')}}</h6>
                        <small class="text-muted">{{translate('messages.updates_existing_offers')}}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script_2')
<script>
$(document).ready(function() {
    $('#importBtn').click(function() {
        startImport();
    });

    $('#importAgainBtn').click(function() {
        resetImportForm();
    });

    function startImport() {
        // إخفاء قسم الاستيراد وإظهار قسم التقدم
        $('#importCard').hide();
        $('#progressContainer').show();
        $('#resultsContainer').hide();

        // إرسال طلب الاستيراد
        $.ajax({
            url: '{{route("admin.offer.import.start")}}',
            type: 'POST',
            data: {
                _token: '{{csrf_token()}}'
            },
            success: function(response) {
                $('#progressContainer').hide();
                
                if (response.success) {
                    showResults(response.results);
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                    resetImportForm();
                }
            },
            error: function(xhr, status, error) {
                $('#progressContainer').hide();
                toastr.error('{{translate("messages.something_went_wrong")}}');
                resetImportForm();
            }
        });
    }

    function showResults(results) {
        let html = '';
        
        if (results.imported > 0) {
            html += `<div class="result-item result-success">
                <span><i class="tio-checkmark-circle"></i> {{translate('messages.imported_offers')}}</span>
                <span class="badge badge-success">${results.imported}</span>
            </div>`;
        }
        
        if (results.updated > 0) {
            html += `<div class="result-item result-warning">
                <span><i class="tio-edit"></i> {{translate('messages.updated_offers')}}</span>
                <span class="badge badge-warning">${results.updated}</span>
            </div>`;
        }
        
        if (results.skipped > 0) {
            html += `<div class="result-item result-warning">
                <span><i class="tio-skip-forward"></i> {{translate('messages.skipped_offers')}}</span>
                <span class="badge badge-secondary">${results.skipped}</span>
            </div>`;
        }
        
        if (results.errors > 0) {
            html += `<div class="result-item result-error">
                <span><i class="tio-error"></i> {{translate('messages.errors')}}</span>
                <span class="badge badge-danger">${results.errors}</span>
            </div>`;
        }

        $('#resultsContent').html(html);
        $('#resultsContainer').show();
    }

    function resetImportForm() {
        $('#importCard').show();
        $('#progressContainer').hide();
        $('#resultsContainer').hide();
    }
});
</script>
@endpush
